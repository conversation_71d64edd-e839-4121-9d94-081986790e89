#!/usr/bin/env zx

import dotenv from 'dotenv';
import { $, echo, fs, path } from 'zx';

// Load environment variables from .env file
dotenv.config();

// Configuration
const TARGET_PROJECTS = ['BLiP', 'Operações'];
const TEMP_DIR = 'temp_repos';

// Language/Framework detection patterns
const LANGUAGE_PATTERNS = {
  // JavaScript/TypeScript
  'package.json': { language: 'JavaScript', framework: 'Node.js' },
  'tsconfig.json': { language: 'TypeScript', framework: 'TypeScript' },
  'angular.json': { language: 'TypeScript', framework: 'Angular' },
  'next.config.js': { language: 'JavaScript', framework: 'Next.js' },
  'nuxt.config.js': { language: 'JavaScript', framework: 'Nuxt.js' },
  'vue.config.js': { language: 'JavaScript', framework: 'Vue.js' },
  'svelte.config.js': { language: 'JavaScript', framework: 'Svelte' },
  'gatsby-config.js': { language: 'JavaScript', framework: 'Gatsby' },
  'webpack.config.js': { language: 'JavaScript', framework: 'Webpack' },
  'vite.config.js': { language: 'JavaScript', framework: 'Vite' },

  // Python
  'requirements.txt': { language: 'Python', framework: 'Python' },
  'setup.py': { language: 'Python', framework: 'Python' },
  'pyproject.toml': { language: 'Python', framework: 'Python' },
  'Pipfile': { language: 'Python', framework: 'Python' },
  'manage.py': { language: 'Python', framework: 'Django' },
  'app.py': { language: 'Python', framework: 'Flask' },
  'main.py': { language: 'Python', framework: 'Python' },

  // .NET
  '*.csproj': { language: 'C#', framework: '.NET' },
  '*.sln': { language: 'C#', framework: '.NET' },
  'Program.cs': { language: 'C#', framework: '.NET' },
  'Startup.cs': { language: 'C#', framework: 'ASP.NET' },
  'appsettings.json': { language: 'C#', framework: '.NET' },

  // Java
  'pom.xml': { language: 'Java', framework: 'Maven' },
  'build.gradle': { language: 'Java', framework: 'Gradle' },
  'build.gradle.kts': { language: 'Kotlin', framework: 'Gradle' },
  'application.properties': { language: 'Java', framework: 'Spring Boot' },
  'application.yml': { language: 'Java', framework: 'Spring Boot' },

  // Go
  'go.mod': { language: 'Go', framework: 'Go' },
  'main.go': { language: 'Go', framework: 'Go' },

  // Rust
  'Cargo.toml': { language: 'Rust', framework: 'Rust' },

  // PHP
  'composer.json': { language: 'PHP', framework: 'PHP' },
  'artisan': { language: 'PHP', framework: 'Laravel' },
  'wp-config.php': { language: 'PHP', framework: 'WordPress' },

  // Ruby
  'Gemfile': { language: 'Ruby', framework: 'Ruby' },
  'config.ru': { language: 'Ruby', framework: 'Rack' },
  'Rakefile': { language: 'Ruby', framework: 'Ruby' },

  // Docker
  'Dockerfile': { language: 'Docker', framework: 'Docker' },
  'docker-compose.yml': { language: 'Docker', framework: 'Docker Compose' },
  'docker-compose.yaml': { language: 'Docker', framework: 'Docker Compose' },

  // Infrastructure
  'terraform.tf': { language: 'HCL', framework: 'Terraform' },
  'main.tf': { language: 'HCL', framework: 'Terraform' },
  'Chart.yaml': { language: 'YAML', framework: 'Helm' },
  'values.yaml': { language: 'YAML', framework: 'Helm' },
  'ansible.cfg': { language: 'YAML', framework: 'Ansible' },
  'playbook.yml': { language: 'YAML', framework: 'Ansible' },

  // Mobile
  'pubspec.yaml': { language: 'Dart', framework: 'Flutter' },
  'ios/': { language: 'Swift/Objective-C', framework: 'iOS' },
  'android/': { language: 'Java/Kotlin', framework: 'Android' },
  'react-native.config.js': { language: 'JavaScript', framework: 'React Native' },
  'expo.json': { language: 'JavaScript', framework: 'Expo' },
  'app.json': { language: 'JavaScript', framework: 'React Native/Expo' },
  
};

async function detectLanguageAndFramework(repoPath) {
  try {
    const detectedTechs = [];

    // Check for specific files and patterns
    for (const [pattern, tech] of Object.entries(LANGUAGE_PATTERNS)) {
      try {
        if (pattern.endsWith('/')) {
          // Handle directory patterns
          const dirExists = await fs.pathExists(path.join(repoPath, pattern));
          if (dirExists) {
            detectedTechs.push(tech);
          }
        } else {
          // Handle glob patterns
          const files = await $`find ${repoPath} -name ${pattern} 2>/dev/null || true`;
          if (files.stdout.trim()) {
            detectedTechs.push(tech);
          } else {
            // Handle specific file checks
            const filePath = path.join(repoPath, pattern);
            const fileExists = await fs.pathExists(filePath);
            if (fileExists) {
              detectedTechs.push(tech);
            }
          }
        }
      } catch (error) {
        // Continue checking other patterns
      }
    }

    // If multiple technologies detected, prioritize by specificity
    if (detectedTechs.length === 0) {
      return { language: 'Unknown', framework: 'Unknown' };
    }

    // Prioritize more specific frameworks
    const priorityOrder = [
      '.NET', 'ASP.NET', 'Angular', 'React', 'Vue.js', 'Next.js', 'Nuxt.js', 'Django', 'Flask',
      'Spring Boot', 'Laravel', 'Flutter', 'React Native', 'Expo', 'Vite', 'Node.js'
    ];

    for (const priority of priorityOrder) {
      const found = detectedTechs.find(tech => tech.framework === priority);
      if (found) {
        return found;
      }
    }

    // Return the first detected technology
    return detectedTechs[0];

  } catch (error) {
    echo`    ⚠️  Error detecting language/framework: ${error.message}`;
    return { language: 'Error', framework: 'Error' };
  }
}

async function cloneAndAnalyzeRepo(repoUrl, repoName) {
  const tempRepoPath = path.join(TEMP_DIR, repoName);

  try {
    echo`    🔄 Cloning ${repoName}...`;

    // Clone repository using SSH
    await $`git clone ${repoUrl} ${tempRepoPath} --depth 1 --quiet`;

    echo`    🔍 Analyzing ${repoName}...`;

    // Detect language and framework
    const tech = await detectLanguageAndFramework(tempRepoPath);

    echo`    ✅ ${repoName}: ${tech.language} (${tech.framework})`;

    return tech;

  } catch (error) {
    echo`    ❌ Error processing ${repoName}: ${error.message}`;
    return { language: 'Error', framework: 'Error' };
  } finally {
    // Clean up cloned repository
    try {
      await $`rm -rf ${tempRepoPath}`;
    } catch (cleanupError) {
      echo`    ⚠️  Warning: Could not clean up ${tempRepoPath}`;
    }
  }
}

async function main() {
  try {
    echo`🚀 Starting repository analysis for BLiP and Operações projects...`;
    echo``;

    // Find the most recent CSV file
    const csvFiles = await $`ls -t azure-devops-projects-*.csv 2>/dev/null || echo ""`;
    const csvFileList = csvFiles.stdout.trim().split('\n').filter(f => f);

    if (csvFileList.length === 0) {
      echo`❌ No CSV files found. Please run 'npm run generate:csv' first.`;
      process.exit(1);
    }

    const inputCsvFile = csvFileList[0];
    echo`📄 Reading CSV file: ${inputCsvFile}`;

    // Read and parse CSV
    const csvContent = await fs.readFile(inputCsvFile, 'utf8');
    const lines = csvContent.split('\n').filter(line => line.trim());

    if (lines.length <= 1) {
      echo`❌ CSV file appears to be empty or only contains headers.`;
      process.exit(1);
    }

    // Parse header and data
    const header = lines[0].split(',').map(col => col.replace(/"/g, ''));
    const rows = lines.slice(1).map(line => {
      // Simple CSV parsing (handles quoted fields)
      const cols = [];
      let current = '';
      let inQuotes = false;

      for (let i = 0; i < line.length; i++) {
        const char = line[i];
        if (char === '"') {
          inQuotes = !inQuotes;
        } else if (char === ',' && !inQuotes) {
          cols.push(current.trim());
          current = '';
        } else {
          current += char;
        }
      }
      cols.push(current.trim());

      return cols;
    });

    echo`📊 Total repositories in CSV: ${rows.length}`;

    // Filter for target projects
    const filteredRows = rows.filter(row => {
      const projectName = row[0] || '';
      return TARGET_PROJECTS.some(target =>
        projectName.toLowerCase() == target.toLowerCase()
      );
    });

    echo`🎯 Filtered repositories for BLiP and Operações: ${filteredRows.length}`;

    if (filteredRows.length === 0) {
      echo`❌ No repositories found for projects: ${TARGET_PROJECTS.join(', ')}`;
      process.exit(1);
    }

    // Create temp directory
    await $`mkdir -p ${TEMP_DIR}`;

    // Prepare enhanced CSV data
    const enhancedHeader = [...header, 'Language', 'Framework'];
    const enhancedRows = [];

    echo``;
    echo`🔍 Analyzing repositories...`;

    // Process each filtered repository
    for (let i = 0; i < filteredRows.length; i++) {
      const row = filteredRows[i];
      const projectName = row[0] || '';
      const repoName = row[3] || '';
      const repoSshUrl = row[6] || ''; // Use SSH URL from CSV column

      echo`📁 [${i + 1}/${filteredRows.length}] Processing: ${projectName}/${repoName}`;

      if (!repoSshUrl || repoName === 'No repositories' || repoName === 'Error fetching repositories') {
        echo`    ⏭️  Skipping (no valid repository)`;
        enhancedRows.push([...row, 'N/A', 'N/A']);
        continue;
      }

      const tech = await cloneAndAnalyzeRepo(repoSshUrl, `${projectName}-${repoName}`.replace(/[^a-zA-Z0-9-]/g, '-'));
      enhancedRows.push([...row, tech.language, tech.framework]);

      echo``;
    }

    // Generate enhanced CSV content
    const enhancedCsvContent = [enhancedHeader, ...enhancedRows]
      .map(row =>
        row.map(field => {
          const escaped = String(field).replace(/"/g, '""');
          return /[",\n\r]/.test(escaped) ? `"${escaped}"` : escaped;
        }).join(',')
      ).join('\n');

    // Write enhanced CSV file
    const outputCsvFile = `blip-operacoes-repos-analyzed-${new Date().toISOString().split('T')[0]}.csv`;
    await fs.writeFile(outputCsvFile, enhancedCsvContent);

    // Clean up temp directory
    await $`rm -rf ${TEMP_DIR}`;

    echo`✅ Enhanced CSV file generated: ${outputCsvFile}`;
    echo`📊 Total analyzed repositories: ${enhancedRows.length}`;
    echo``;
    echo`🎉 Analysis completed successfully!`;

    // Display technology summary
    const techSummary = {};
    enhancedRows.forEach(row => {
      const framework = row[row.length - 1];
      if (framework && framework !== 'N/A' && framework !== 'Error') {
        techSummary[framework] = (techSummary[framework] || 0) + 1;
      }
    });

    echo`📈 Technology Summary:`;
    Object.entries(techSummary)
      .sort(([, a], [, b]) => b - a)
      .forEach(([tech, count]) => {
        echo`  - ${tech}: ${count} repositories`;
      });

  } catch (error) {
    echo`❌ Error: ${error.message}`;
    process.exit(1);
  } finally {
    // Ensure cleanup
    try {
      await $`rm -rf ${TEMP_DIR}`;
    } catch (cleanupError) {
      // Ignore cleanup errors
    }
  }
}

// Run the main function
main().catch(error => {
  echo`💥 Unexpected error: ${error.message}`;
  process.exit(1);
});
