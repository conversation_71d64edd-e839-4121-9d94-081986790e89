# Azure DevOps Project and Repository Listing Tool

This Node.js project uses the `zx` scripting tool and `azure-devops-node-api` to list all projects and repositories from the Curupira organization in Azure DevOps, then consolidates the data into a CSV file.

## Features

- 📋 Lists all projects in the Curupira organization
- 📚 Lists all Git repositories for each project
- 📊 Generates a comprehensive CSV report
- 🔍 Includes project and repository metadata
- ⚡ Built with zx for easy scripting
- 🛡️ Proper error handling and authentication

## Prerequisites

- Node.js (version 14 or higher)
- Azure DevOps Personal Access Token with appropriate permissions

## Installation

1. Install dependencies:
```bash
npm install
```

## Setup

### Azure DevOps Personal Access Token

1. Go to Azure DevOps: https://dev.azure.com/curupira
2. Click on your profile picture → Personal access tokens
3. Create a new token with the following scopes:
   - **Project and Team**: Read
   - **Code**: Read (for repository access)
4. Copy the generated token

### Environment Variable

Set your Azure DevOps Personal Access Token as an environment variable:

```bash
export AZURE_DEVOPS_TOKEN="your-personal-access-token-here"
```

Or create a `.env` file (not recommended for production):
```bash
echo "AZURE_DEVOPS_TOKEN=your-personal-access-token-here" > .env
```

## Usage

### Generate Initial CSV

Run the script to generate the complete CSV of all projects and repositories:

```bash
npm run generate:csv
```

### Analyze BLiP and Operações Repositories

Run the analysis script to filter and analyze repositories from BLiP and Operações projects:

```bash
npm run analyze:repos
```

This script will:
1. Read the most recent generated CSV file
2. Filter repositories from "BLiP" and "Operações" projects
3. Clone each repository using SSH
4. Detect the programming language and framework
5. Generate an enhanced CSV with language/framework information
6. Clean up cloned repositories to save disk space

## Output

The script will:

1. Connect to Azure DevOps using your token
2. Fetch all projects from the Curupira organization
3. For each project, fetch all Git repositories
4. Generate a CSV file named `azure-devops-projects-YYYY-MM-DD.csv`

### CSV Structure

#### Initial CSV (generate:csv)

The generated CSV contains the following columns:

| Column | Description |
|--------|-------------|
| Project Name | Name of the Azure DevOps project |
| Project ID | Unique identifier for the project |
| Project Description | Project description (if available) |
| Repository Name | Name of the Git repository |
| Repository ID | Unique identifier for the repository |
| Repository URL | Web URL to access the repository |
| Default Branch | Default branch name (usually 'main' or 'master') |
| Last Commit Date | Date of the last commit |
| Last Commit Author | Author of the last commit |
| Last Commit Email | Email of the last commit author |

#### Enhanced CSV (analyze:repos)

The enhanced CSV for BLiP and Operações projects includes all the above columns plus:

| Column | Description |
|--------|-------------|
| Language | Detected programming language |
| Framework | Detected framework or technology stack |

## Language/Framework Detection

The `analyze:repos` script can detect the following technologies:

### Programming Languages
- **JavaScript/TypeScript**: Node.js, Angular, React, Vue.js, Next.js, Nuxt.js, Svelte, Gatsby
- **Python**: Django, Flask, general Python projects
- **C#**: .NET, ASP.NET applications
- **Java/Kotlin**: Maven, Gradle, Spring Boot projects
- **Go**: Go modules and applications
- **Rust**: Cargo-based projects
- **PHP**: Laravel, WordPress, general PHP projects
- **Ruby**: Rails, Rack applications
- **Dart**: Flutter applications
- **Swift/Objective-C**: iOS applications

### Infrastructure & DevOps
- **Docker**: Dockerfile, Docker Compose
- **Terraform**: Infrastructure as Code
- **Helm**: Kubernetes package manager
- **Ansible**: Configuration management

### Mobile Development
- **React Native/Expo**: Cross-platform mobile apps
- **Flutter**: Cross-platform mobile apps
- **Native iOS/Android**: Platform-specific mobile apps

The detection works by analyzing configuration files, project structure, and common patterns in each repository.

## Error Handling

The script includes comprehensive error handling for:

- Missing authentication token
- Network connectivity issues
- Authentication failures (401)
- Permission issues (403)
- Projects with no repositories
- API errors when fetching repository data

## Example Output

```
🚀 Starting Azure DevOps project and repository listing...
Organization: curupira

📋 Fetching all projects...
✅ Found 5 projects

📁 Processing project: Project Alpha
  📚 Found 3 repositories
    - alpha-frontend
    - alpha-backend
    - alpha-docs

📁 Processing project: Project Beta
  📚 Found 2 repositories
    - beta-api
    - beta-web

✅ CSV file generated: azure-devops-projects-2024-01-15.csv
📊 Total rows: 5 (excluding header)

🎉 Process completed successfully!

📈 Summary:
  - Projects: 5
  - Repositories: 5
  - CSV file: azure-devops-projects-2024-01-15.csv
```

## Troubleshooting

### Authentication Issues

- Ensure your Personal Access Token is valid and not expired
- Verify the token has the correct scopes (Project and Team: Read, Code: Read)
- Check that you have access to the Curupira organization

### Permission Issues

- Verify you're a member of the Curupira organization
- Ensure your account has read access to the projects and repositories

### Network Issues

- Check your internet connection
- Verify you can access https://dev.azure.com/curupira in your browser

## Dependencies

- `zx`: Google's zx scripting tool for better shell scripting with JavaScript
- `azure-devops-node-api`: Official Azure DevOps API client for Node.js

## License

ISC
