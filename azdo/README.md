# Azure DevOps Project and Repository Listing Tool

This Node.js project uses the `zx` scripting tool and `azure-devops-node-api` to list all projects and repositories from the Curupira organization in Azure DevOps, then consolidates the data into a CSV file.

## Features

- 📋 Lists all projects in the Curupira organization
- 📚 Lists all Git repositories for each project
- 📊 Generates a comprehensive CSV report
- 🔍 Includes project and repository metadata
- ⚡ Built with zx for easy scripting
- 🛡️ Proper error handling and authentication

## Prerequisites

- Node.js (version 14 or higher)
- Azure DevOps Personal Access Token with appropriate permissions

## Installation

1. Install dependencies:
```bash
npm install
```

## Setup

### Azure DevOps Personal Access Token

1. Go to Azure DevOps: https://dev.azure.com/curupira
2. Click on your profile picture → Personal access tokens
3. Create a new token with the following scopes:
   - **Project and Team**: Read
   - **Code**: Read (for repository access)
4. Copy the generated token

### Environment Variable

Set your Azure DevOps Personal Access Token as an environment variable:

```bash
export AZURE_DEVOPS_TOKEN="your-personal-access-token-here"
```

Or create a `.env` file (not recommended for production):
```bash
echo "AZURE_DEVOPS_TOKEN=your-personal-access-token-here" > .env
```

## Usage

Run the script:

```bash
npm start
```

Or directly with node:

```bash
node index.mjs
```

## Output

The script will:

1. Connect to Azure DevOps using your token
2. Fetch all projects from the Curupira organization
3. For each project, fetch all Git repositories
4. Generate a CSV file named `azure-devops-projects-YYYY-MM-DD.csv`

### CSV Structure

The generated CSV contains the following columns:

| Column | Description |
|--------|-------------|
| Project Name | Name of the Azure DevOps project |
| Project ID | Unique identifier for the project |
| Project Description | Project description (if available) |
| Repository Name | Name of the Git repository |
| Repository ID | Unique identifier for the repository |
| Repository URL | Web URL to access the repository |
| Default Branch | Default branch name (usually 'main' or 'master') |

## Error Handling

The script includes comprehensive error handling for:

- Missing authentication token
- Network connectivity issues
- Authentication failures (401)
- Permission issues (403)
- Projects with no repositories
- API errors when fetching repository data

## Example Output

```
🚀 Starting Azure DevOps project and repository listing...
Organization: curupira

📋 Fetching all projects...
✅ Found 5 projects

📁 Processing project: Project Alpha
  📚 Found 3 repositories
    - alpha-frontend
    - alpha-backend
    - alpha-docs

📁 Processing project: Project Beta
  📚 Found 2 repositories
    - beta-api
    - beta-web

✅ CSV file generated: azure-devops-projects-2024-01-15.csv
📊 Total rows: 5 (excluding header)

🎉 Process completed successfully!

📈 Summary:
  - Projects: 5
  - Repositories: 5
  - CSV file: azure-devops-projects-2024-01-15.csv
```

## Troubleshooting

### Authentication Issues

- Ensure your Personal Access Token is valid and not expired
- Verify the token has the correct scopes (Project and Team: Read, Code: Read)
- Check that you have access to the Curupira organization

### Permission Issues

- Verify you're a member of the Curupira organization
- Ensure your account has read access to the projects and repositories

### Network Issues

- Check your internet connection
- Verify you can access https://dev.azure.com/curupira in your browser

## Dependencies

- `zx`: Google's zx scripting tool for better shell scripting with JavaScript
- `azure-devops-node-api`: Official Azure DevOps API client for Node.js

## License

ISC
