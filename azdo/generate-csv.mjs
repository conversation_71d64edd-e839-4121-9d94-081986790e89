#!/usr/bin/env zx

import { $, echo, fs } from 'zx';
import * as azdev from 'azure-devops-node-api';

// Configuration
const ORGANIZATION = 'curupira';
const AZURE_DEVOPS_URL = `https://dev.azure.com/${ORGANIZATION}`;

// Environment variables for authentication
const AZURE_DEVOPS_TOKEN = process.env.AZURE_DEVOPS_TOKEN;

if (!AZURE_DEVOPS_TOKEN) {
  echo`❌ Error: AZURE_DEVOPS_TOKEN environment variable is required`;
  echo`Please set your Azure DevOps Personal Access Token:`;
  echo`export AZURE_DEVOPS_TOKEN="your-token-here"`;
  process.exit(1);
}

async function main() {
  try {
    echo`🚀 Starting Azure DevOps project and repository listing...`;
    echo`Organization: ${ORGANIZATION}`;
    echo``;

    // Create connection to Azure DevOps
    const authHandler = azdev.getPersonalAccessTokenHandler(AZURE_DEVOPS_TOKEN);
    const connection = new azdev.WebApi(AZURE_DEVOPS_URL, authHandler);

    // Get Core API for projects
    const coreApi = await connection.getCoreApi();
    
    // Get Git API for repositories
    const gitApi = await connection.getGitApi();

    echo`📋 Fetching all projects...`;
    const projects = await coreApi.getProjects();
    
    if (!projects || projects.length === 0) {
      echo`❌ No projects found in organization ${ORGANIZATION}`;
      return;
    }

    echo`✅ Found ${projects.length} projects`;
    echo``;

    // Array to store all data for CSV
    const csvData = [];
    
    // Add CSV header
    csvData.push([
      'Project Name',
      'Project ID',
      'Project Description',
      'Repository Name',
      'Repository ID',
      'Repository URL',
      'Default Branch',
      'Last Commit Date',
      'Last Commit Author',
      'Last Commit Email'
    ]);

    // Process each project
    for (const project of projects) {
      echo`📁 Processing project: ${project.name}`;
      
      try {
        // Get repositories for this project
        const repositories = await gitApi.getRepositories(project.id);
        
        if (!repositories || repositories.length === 0) {
          echo`  ⚠️  No repositories found in project ${project.name}`;
          // Add project row with no repository data
          csvData.push([
            project.name || '',
            project.id || '',
            project.description || '',
            'No repositories',
            '',
            '',
            ''
          ]);
        } else {
          echo`  📚 Found ${repositories.length} repositories`;
          
          // Add a row for each repository
          for (const repo of repositories) {
            let lastCommitDate = '';
            let lastCommitAuthor = '';
            let lastCommitEmail = '';
            if (repo.defaultBranch) {
              try {
                const branchName = repo.defaultBranch.replace('refs/heads/', '');
                const commits = await gitApi.getCommits(
                  repo.id,
                  { $top: 1, itemVersion: { versionType: 'branch', version: branchName } },
                  project.id
                );
                if (commits && commits.length > 0) {
                  const commit = commits[0];
                  lastCommitDate = commit.author?.date || '';
                  lastCommitAuthor = commit.author?.name || '';
                  lastCommitEmail = commit.author?.email || '';
                } else {
                  lastCommitDate = 'No commits';
                  lastCommitAuthor = '';
                  lastCommitEmail = '';
                }
              } catch (commitError) {
                lastCommitDate = 'Error fetching commit';
                lastCommitAuthor = '';
                lastCommitEmail = '';
                echo`      ⚠️  Error fetching last commit for ${repo.name}: ${commitError.message}`;
              }
            } else {
              lastCommitDate = 'No default branch';
            }
            csvData.push([
              project.name || '',
              project.id || '',
              project.description || '',
              repo.name || '',
              repo.id || '',
              repo.webUrl || '',
              repo.defaultBranch || '',
              lastCommitDate,
              lastCommitAuthor,
              lastCommitEmail
            ]);
            echo`    - ${repo.name}`;
          }
        }
      } catch (repoError) {
        echo`  ❌ Error fetching repositories for project ${project.name}: ${repoError.message}`;
        // Add project row with error indication
        csvData.push([
          project.name || '',
          project.id || '',
          project.description || '',
          'Error fetching repositories',
          '',
          '',
          ''
        ]);
      }
      
      echo``;
    }

    // Generate CSV content
    const csvContent = csvData.map(row => 
      row.map(field => {
        // Escape quotes and wrap in quotes if field contains comma, quote, or newline
        const escaped = String(field).replace(/"/g, '""');
        return /[",\n\r]/.test(escaped) ? `"${escaped}"` : escaped;
      }).join(',')
    ).join('\n');

    // Write CSV file
    const csvFileName = `azure-devops-projects-${new Date().toISOString().split('T')[0]}.csv`;
    await fs.writeFile(csvFileName, csvContent);

    echo`✅ CSV file generated: ${csvFileName}`;
    echo`📊 Total rows: ${csvData.length - 1} (excluding header)`;
    echo``;
    echo`🎉 Process completed successfully!`;

    // Display summary
    const projectCount = projects.length;
    const repoCount = csvData.length - 1 - csvData.filter(row => row[3] === 'No repositories' || row[3] === 'Error fetching repositories').length;
    
    echo`📈 Summary:`;
    echo`  - Projects: ${projectCount}`;
    echo`  - Repositories: ${repoCount}`;
    echo`  - CSV file: ${csvFileName}`;

  } catch (error) {
    echo`❌ Error: ${error.message}`;
    if (error.statusCode === 401) {
      echo`🔑 Authentication failed. Please check your AZURE_DEVOPS_TOKEN`;
    } else if (error.statusCode === 403) {
      echo`🚫 Access denied. Please check your permissions for organization ${ORGANIZATION}`;
    }
    process.exit(1);
  }
}

// Run the main function
main().catch(error => {
  echo`💥 Unexpected error: ${error.message}`;
  process.exit(1);
});
